import { Menu, X, <PERSON>, PhoneCall } from "lucide-react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";

export default function Navbar() {
  const navLinks = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products" },
    { name: "News", path: "/news" },
    { name: "Applications", path: "/applications" },
    { name: "About", path: "/about" },
    { name: "Contact", path: "/contact" },
  ];

  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const isActive = (path) => location.pathname === path;

  return (
    <header className="bg-white shadow-md sticky top-0 z-50 border-b">
      <div className="navbar px-4 lg:px-8 max-w-screen-2xl mx-auto flex justify-between items-center relative">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <span className="text-[#ce161b] text-3xl font-extrabold">PY</span>
          <div className="leading-tight">
            <h1 className="text-xl font-bold">Pyramid Power</h1>
            <p className="text-[0.7rem] text-gray-500 font-medium">
              hafez compressors
            </p>
          </div>
        </div>

        {/* Desktop Nav - Centered */}
        <div className="hidden lg:flex absolute left-1/2 transform -translate-x-1/2">
          <ul className="flex gap-6 text-[0.95rem] font-medium">
            {navLinks.map((link) => (
              <li key={link.name}>
                <Link
                  to={link.path}
                  className={`hover:text-[#ce161b] transition relative ${
                    isActive(link.path)
                      ? "text-[#ce161b] font-semibold"
                      : "text-black"
                  }`}
                >
                  {link.name}
                  <span
                    className={`absolute left-0 -bottom-1 h-0.5 bg-[#ce161b] transition-all duration-300 ${
                      isActive(link.path) ? "w-full" : "w-0 group-hover:w-full"
                    }`}
                  ></span>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Right side - Contact Info & Mobile Menu */}
        <div className="flex items-center">
          {/* Contact Info - Desktop only */}
          <div className="hidden lg:flex items-center gap-2 text-sm">
            <PhoneCall className="text-[#ce161b] w-5 h-5" />
            <div className="leading-tight">
              <p className="text-gray-500 text-xs">Get A Quote</p>
              <a
                href="mailto:<EMAIL>"
                className="hover:text-[#ce161b] font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button className="btn btn-ghost" onClick={() => setIsOpen(true)}>
              <Menu className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      {/* Backdrop */}
      <div
        className={`fixed inset-0 z-40 transition-all duration-300 ease-in-out ${
          isOpen
            ? "backdrop-blur-sm bg-opacity-40 pointer-events-auto"
            : "backdrop-blur-none bg-opacity-0 pointer-events-none"
        }`}
        onClick={() => setIsOpen(false)}
      />

      {/* Sidebar */}
      <div
        className={`fixed top-0 right-0 h-full bg-white shadow-2xl z-50 transform transition-all duration-300 ease-in-out ${
          isOpen ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
        } w-[75%] sm:w-[60%] md:w-[40%] lg:w-[30%]`}
      >
        <div className="flex items-center justify-between p-6 border-b border-base-200">
          <h2 className="text-xl font-bold text-[#ce161b]">Menu</h2>
          <button
            className="btn btn-sm btn-circle btn-ghost hover:bg-base-200"
            onClick={() => setIsOpen(false)}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className="flex flex-col p-6 space-y-2">
          {navLinks.map((link) => (
            <Link
              key={link.name}
              to={link.path}
              onClick={() => setIsOpen(false)}
              className={`group flex items-center py-4 px-4 rounded-lg text-lg font-medium
                hover:bg-[#ce161b] hover:text-white transition-all duration-200
                ${
                  isActive(link.path)
                    ? "bg-[#ce161b]/10 text-[#ce161b]"
                    : "text-black"
                }`}
            >
              {link.name}
            </Link>
          ))}
        </nav>
      </div>
    </header>
  );
}
